{% extends 'product_base.html' %}
{% block home %}


<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css"
    integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

        
<div class="col-xs-6 col-sm-9">
    <!-- {{ current_builds }} -->
    <br>
    <div class="row">

        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">UT Coverage for an ongoing feature</h5>
                    <p class="card-text">For features in development branch </p>
                    <a href="/ongoingfeature/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>

        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">UT Coverage for a feature that has been merged into the main branch</h5>
                    <p class="card-text">For features already merged into main</p>
                    <a href="/mergedfeature//{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Regenerate Sonar Unit Test report for a merged feature</h5>
                    <p class="card-text">After resolving UT failures</p>
                    <a href="/generateFeatureReport/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>


    </div>


    {% if feature_data %}
    <div class="fw-body" >
        <div class="content">
            <br>
            <br>
            <h3 style="text-align:left">View Feature Coverage Data for {{product.upper()}}</h3>
            <br>

            <div class="form-group row">
                <label for="release_version" class="col-sm-1 col-form-label">Release Version:</label>
                <div class="col-sm-2">
                    <form method="GET" action="{{ url_for('sonar.feature_coverage', product=product) }}" id="version_form">
                        <select name="release_version" id="release_version" class="form-control" style="width: 150px;"
                            onchange="this.form.submit()">
                            <option value="all">All Versions</option>
                            {% for version in release_versions %}
                            <option value="{{ version }}" {% if selected_version==version %}selected{% endif %}>{{ version }}
                            </option>
                            {% endfor %}
                        </select>
                    </form>
                </div>

                <label for="search_feature" class="col-sm-1 col-form-label">Search Feature:</label>
                <div class="col-sm-3">
                    <input type="text" id="search_feature" name="search_feature" class="form-control"
                        placeholder="Enter Feature ID or Name" onkeyup="filterTable()">
                </div>
            </div>
            
            <table id="feature_coverage" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col">Release Version</th>
                        <th class="th-sm" scope="col">Feature ID</th>
                        <th class="th-sm" scope="col">Analysis Date</th>
                        <th class="th-sm" scope="col">Sonar Link</th>
                        <th class="th-sm" scope="col">UT Coverage</th>
                        <th class="th-sm" scope="col">Dev LOC</th>
                        <th class="th-sm" scope="col">UT LOC</th>
                        <th class="th-sm" scope="col">Bugs</th>
                        <th class="th-sm" scope="col">Vulnerabilities</th>
                        <th class="th-sm" scope="col">Code Smells</th>
                    </tr>
                </thead>
                <tbody>
                    {% for data in feature_data %}
                    <tr style="text-align:center" class="accordion-toggle" data-toggle="collapse"
                        data-target="#sonar_{{ data.feat_id }}">
                        <td>{{ data.release_version }}</td>
                        <td>
                            {{ data.feat_id }}
                            {% if data.sonar_app_name %}
                            <button class="btn btn-sm" type="button" data-toggle="collapse"
                                data-target="#sonar_{{ data.feat_id }}" aria-expanded="false"
                                aria-controls="sonar_{{ data.feat_id }}">
                                <i class="fas fa-caret-down"></i>
                            </button>
                            {% endif %}
                        </td>
                        <td>{{ data.analysis_date }}</td>
                        <td>
                            {% if data.sonar_app_name and data.sonar_app_branch %}
                            <a href="https://engci-sonar-sjc.cisco.com/sonar/dashboard?branch={{ data.sonar_app_branch }}&id={{ data.sonar_app_name }}"
                                target="_blank">
                                {{ data.sonar_app_branch }}
                            </a>
                            {% else %}
                            <span class="text-secondary">N/A</span>
                            {% endif %}
                        </td>
                        {% if data.ut_new is not none %}
                        {% if data.ut_new >= 90 %}
                        <td style="color:green;font-weight:bold">{{ data.ut_new }}%</td>
                        {% elif data.ut_new >= 50 %}
                        <td style="color:orange;font-weight:bold">{{ data.ut_new }}%</td>
                        {% else %}
                        <td style="color:tomato;font-weight:bold">{{ data.ut_new }}%</td>
                        {% endif %}
                        {% else %}
                        <td>
                            <span class="text-secondary">N/A</span>
                        </td>
                        {% endif %}
                        <td>{{ data.loc_new }}</td>
                        <td>{{ data.ut_loc if data.ut_loc is not none else 'N/A' }}</td>
                        <td>{{ data.bugs }}</td>
                        <td>{{ data.vulnerabilities }}</td>
                        <td>{{ data.code_smell }}</td>
                    </tr>
                    {% if data.sonar_projects %}
                    <tr>
                        <td colspan="10" class="hiddenRow" style="padding:0;">
                            <div class="accordian-body collapse" id="sonar_{{ data.feat_id }}">
                                <table class="table table-striped table-bordered nested-table" style="margin:0;">
                                    <thead>
                                        <tr style="text-align:center; background-color:#f0f0f0;color:black">
                                            <th scope="col">Sonar Project</th>
                                            <th scope="col">UT Coverage</th>
                                            <th scope="col">Dev LOC</th>
                                            <th scope="col">UT LOC</th>
                                            <th scope="col">Bugs</th>
                                            <th scope="col">Vulnerabilities</th>
                                            <th scope="col">Code Smells</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for project in data.sonar_projects %}
                                        <tr style="text-align:center">
                                            <td>
                                                <a href="https://engci-sonar-sjc.cisco.com/sonar/dashboard?id={{ project.key if project.key else project.name }}&branch=dev-cc-{{ data.feat_id }}&code_scope=new"
                                                    target="_blank">
                                                    {{ project.name }}
                                                </a>
                                            </td>
                                            {% if project.ut_coverage is not none %}
                                            {% if project.ut_coverage >= 90 %}
                                            <td style="color:green;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                            {% elif project.ut_coverage >= 50 %}
                                            <td style="color:orange;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                            {% else %}
                                            <td style="color:tomato;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                            {% endif %}
                                            {% else %}
                                            <td>
                                                <span class="text-secondary">N/A</span>
                                            </td>
                                            {% endif %}
                                            <td>{{ project.loc }}</td>
                                            <td>{{ project.ut_loc if project.ut_loc is defined else 'N/A' }}</td>
                                            <td>{{ project.bugs }}</td>
                                            <td>{{ project.vulnerabilities }}</td>
                                            <td>{{ project.code_smells }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
            <script>
                $(document).ready(function () {
                    var table = $('#feature_coverage').DataTable({
                        paging: true,
                        pageLength: 10,
                        order: [[0, "desc"]],
                        searching: true,
                        info: true
                    });

                    $('#search_feature').on('keyup', function () {
                        table.search(this.value).draw();
                    });
                });
               </script>
               <p></p>
               </p>
               <br>
               <p>Note: If a feature has no Sonar project, it will not be displayed in the table.</p>
               <p>      The UT Coverage percentage is color-coded as follows:</p>
            <ul>
                <li style="color:green;font-weight:bold">Green: 90% and above</li>
                <li style="color:orange;font-weight:bold">Orange: 50% to 89%</li>
                <li style="color:tomato;font-weight:bold">Red: Below 50%</li>
            </ul>
            <br>
            <p>Note: The table below shows the latest analysis date for each feature. If a feature has multiple Sonar projects,
                the latest analysis date will be displayed.</p>
            </div>
        </div>
    {% endif %}
{% endblock %}
